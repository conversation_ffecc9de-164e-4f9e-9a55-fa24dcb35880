import tkinter as tk

class ProjectInfoFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        fields = [
            "اسم المشروع",
            "موقع المشروع",
            "قيمة المنحة المطلوبة",
            "مصادر التمويل",
            "قيمة التمويل الذاتي",
            "تكلفة المشروع الكلية",
            "تاريخ تقديم الخطة",
            "أهمية الفكرة/نوع المشروع",
            "المهارات اللازمة",
            "حاجة المجتمع للمشروع",
            "هل يحتاج المشروع لترخيص",
            "جهة الترخيص (إن وجد)",
            "الفئة المستهدفة"
        ]
        tk.Label(self, text="بيانات المشروع الأساسية", font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="e", pady=16, padx=24)
        form = tk.Frame(self, bg="#ffffff")
        form.pack(anchor="e", padx=32)
        self.entries = {}
        for i, label in enumerate(fields):
            tk.Label(form, text=label, font=("Tajawal", 12), bg="#ffffff", width=22, anchor="e").grid(row=i, column=0, sticky="e", pady=6, padx=6)
            ent = tk.Entry(form, font=("Tajawal", 12), width=38, justify="right")
            ent.grid(row=i, column=1, pady=6, padx=6)
            self.entries[label] = ent