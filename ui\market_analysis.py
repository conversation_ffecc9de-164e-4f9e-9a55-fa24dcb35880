import tkinter as tk

class MarketAnalysisFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        fields = [
            "المنتجات",
            "الخدمات",
            "هل يوجد منافسون؟ (عددهم)",
            "المنتجات المنافسة",
            "طرق المنافسة",
            "مقارنة الأسعار",
            "قنوات بيع المنافسين",
            "مراقبة المنافسين (الزبائن/يومياً)",
            "عدد الزبائن المحتملين",
            "معدل الاستهلاك الشهري",
            "حالة العرض والطلب",
            "مواسم الذروة",
            "تميز المنتج/الخدمة",
            "خطة التسويق/البيع",
            "الحاجة لموردين (عددهم)",
            "سهولة الوصول/أسعار الموردين"
        ]
        tk.Label(self, text="دراسة السوق والمنافسين", font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="e", pady=16, padx=24)
        form = tk.Frame(self, bg="#ffffff")
        form.pack(anchor="e", padx=32)
        self.entries = {}
        for i, label in enumerate(fields):
            tk.Label(form, text=label, font=("Tajawal", 12), bg="#ffffff", width=28, anchor="e").grid(row=i, column=0, sticky="e", pady=5, padx=5)
            ent = tk.Entry(form, font=("Tajawal", 12), width=36, justify="right")
            ent.grid(row=i, column=1, pady=5, padx=5)
            self.entries[label] = ent