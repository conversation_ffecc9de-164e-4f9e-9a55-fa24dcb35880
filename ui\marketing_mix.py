import tkinter as tk

class MarketingMixFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        tk.Label(self, text="المزيج التسويقي (4Ps + People)", font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="e", pady=16, padx=24)
        form = tk.Frame(self, bg="#ffffff")
        form.pack(anchor="e", padx=32)
        fields = ["المنتج", "السعر", "المكان", "الترويج", "الأشخاص"]
        self.entries = {}
        for i, label in enumerate(fields):
            tk.Label(form, text=label, font=("Tajawal", 12), bg="#ffffff", width=12, anchor="e").grid(row=i, column=0, sticky="e", pady=6, padx=6)
            txt = tk.Text(form, font=("Tajawal", 12), width=56, height=2)
            txt.grid(row=i, column=1, pady=6, padx=6)
            self.entries[label] = txt