import tkinter as tk

class PersonalInfoFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        fields = [
            "اسم صاحب المشروع",
            "العمر",
            "الحالة الاجتماعية",
            "عدد أفراد الأسرة",
            "المؤهل العلمي",
            "رقم الهاتف",
            "رقم هاتف شخص معرف",
            "مكان السكن"
        ]
        tk.Label(self, text="المعلومات الشخصية لصاحب المشروع", font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="e", pady=16, padx=24)
        form = tk.Frame(self, bg="#ffffff")
        form.pack(anchor="e", padx=32)
        self.entries = {}
        for i, label in enumerate(fields):
            tk.Label(form, text=label, font=("Tajawal", 12), bg="#ffffff", width=22, anchor="e").grid(row=i, column=0, sticky="e", pady=6, padx=6)
            ent = tk.Entry(form, font=("Tajawal", 12), width=38, justify="right")
            ent.grid(row=i, column=1, pady=6, padx=6)
            self.entries[label] = ent