import tkinter as tk
from tkinter import ttk
from ui.personal_info import PersonalInfoFrame
from ui.project_info import ProjectInfoFrame
from ui.market_analysis import MarketAnalysisFrame
from ui.swot import SWOTFrame
from ui.marketing_mix import MarketingMixFrame
from ui.production import ProductionFrame
from ui.financials import FinancialsFrame
from ui.summary import SummaryFrame

SECTIONS = [
    ("المعلومات الشخصية", PersonalInfoFrame),
    ("بيانات المشروع", ProjectInfoFrame),
    ("دراسة السوق والمنافسين", MarketAnalysisFrame),
    ("تحليل SWOT", SWOTFrame),
    ("المزيج التسويقي", MarketingMixFrame),
    ("مستلزمات الإنتاج", ProductionFrame),
    ("الدراسة المالية", FinancialsFrame),
    ("ملخص الربح السنوي", SummaryFrame)
]

class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("نظام دراسة جدوى شامل")
        self.geometry("1250x850")
        self.configure(bg="#f2f4f8")

        # العنوان الرئيسي
        header = tk.Label(self, text="نظام دراسة جدوى شامل", font=("Tajawal", 22, "bold"), bg="#f2f4f8", fg="#173360")
        header.pack(pady=12)

        # إطار رئيسي مقسم: قائمة أقسام يمين + محتوى يسار
        main_frame = tk.Frame(self, bg="#f2f4f8")
        main_frame.pack(fill="both", expand=True)

        # قائمة أقسام
        sidebar = tk.Frame(main_frame, bg="#ffffff", width=220, relief="solid", borderwidth=1)
        sidebar.pack(side="left", fill="y", padx=(0, 1), pady=0)
        sidebar.pack_propagate(0)
        tk.Label(sidebar, text="الأقسام", font=("Tajawal", 14, "bold"), bg="#ffffff", fg="#0d6efd").pack(pady=(18,10))

        # منطقة المحتوى
        self.content_frame = tk.Frame(main_frame, bg="#f8f9fa")
        self.content_frame.pack(side="left", fill="both", expand=True)

        # تحميل القسم الأول افتراضياً
        self.section_frames = {}
        self.current_section = None

        for idx, (section, section_class) in enumerate(SECTIONS):
            btn = tk.Button(sidebar, text=section, font=("Tajawal", 12), anchor="e",
                            bg="#ecf2fa", fg="#17486d", relief="flat", padx=8, pady=8,
                            command=lambda s=section: self.show_section(s))
            btn.pack(fill="x", pady=3, padx=8)
            # أنشئ كل إطار مرة واحدة (للكفاءة)
            frame = section_class(self.content_frame)
            self.section_frames[section] = frame

        self.show_section(SECTIONS[0][0])

    def show_section(self, section_name):
        if self.current_section:
            self.section_frames[self.current_section].pack_forget()
        self.section_frames[section_name].pack(fill="both", expand=True)
        self.current_section = section_name

if __name__ == "__main__":
    app = MainApp()
    app.mainloop()