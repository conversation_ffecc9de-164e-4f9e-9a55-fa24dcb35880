import tkinter as tk

class SWOTFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        tk.Label(self, text="تحليل SWOT الرباعي", font=("<PERSON><PERSON>wal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="center", pady=18)
        grid = tk.Frame(self, bg="#ffffff")
        grid.pack(anchor="center")
        headers = ["نقاط القوة", "نقاط الضعف", "الفرص", "التهديدات"]
        self.entries = {}
        for i, label in enumerate(headers):
            tk.Label(grid, text=label, font=("Tajawal", 12), bg="#e6e8fa", width=22, anchor="center").grid(row=0, column=i, padx=8, pady=4)
            txt = tk.Text(grid, font=("Tajawal", 12), width=23, height=6)
            txt.grid(row=1, column=i, padx=8, pady=6)
            self.entries[label] = txt