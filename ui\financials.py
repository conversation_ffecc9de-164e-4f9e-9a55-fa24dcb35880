import tkinter as tk
from tkinter import ttk

class FinancialsFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        tk.Label(self, text="الدراسة المالية", font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="e", pady=16, padx=24)
        # جداول فرعية
        # تكاليف التأسيس
        tk.Label(self, text="تكاليف التأسيس (ما قبل التشغيل):", font=("Tajawal", 12, "bold"), bg="#ffffff").pack(anchor="w", padx=32)
        self.startup_tree = ttk.Treeview(self, columns=("البند", "المبلغ"), show="headings", height=5)
        for col in self.startup_tree["columns"]:
            self.startup_tree.heading(col, text=col)
            self.startup_tree.column(col, width=150)
        for item in ["رخصة المشروع", "توصيل ماء/كهرباء/هاتف", "رسوم خلو/نقل", "مصاريف أخرى"]:
            self.startup_tree.insert("", "end", values=(item, ""))
        self.startup_tree.pack(padx=28, pady=7)

        # رأس المال الثابت
        tk.Label(self, text="رأس المال الثابت:", font=("Tajawal", 12, "bold"), bg="#ffffff").pack(anchor="w", padx=32, pady=(10,2))
        self.fixed_tree = ttk.Treeview(self, columns=("البند", "المبلغ"), show="headings", height=5)
        for col in self.fixed_tree["columns"]:
            self.fixed_tree.heading(col, text=col)
            self.fixed_tree.column(col, width=150)
        for item in ["تجهيز المحل", "الأثاث", "الآلات والمعدات", "نفقات أخرى"]:
            self.fixed_tree.insert("", "end", values=(item, ""))
        self.fixed_tree.pack(padx=28, pady=7)

        # رأس المال العامل
        tk.Label(self, text="رأس المال العامل (تشغيلي/شهري):", font=("Tajawal", 12, "bold"), bg="#ffffff").pack(anchor="w", padx=32, pady=(10,2))
        self.working_tree = ttk.Treeview(self, columns=("البند", "ثابت/متغير", "المبلغ"), show="headings", height=7)
        for col in self.working_tree["columns"]:
            self.working_tree.heading(col, text=col)
            self.working_tree.column(col, width=120)
        for item in [
            ("رواتب", "ثابت"), ("إيجار", "ثابت"), ("تسويق/دعاية", "ثابت"),
            ("مواد خام", "متغير"), ("أجور عمال", "متغير"), ("فواتير/نقل", "متغير"),
            ("أخرى", "")
        ]:
            self.working_tree.insert("", "end", values=(item[0], item[1], ""))
        self.working_tree.pack(padx=28, pady=7)

        # تقدير الأرباح الشهرية
        tk.Label(self, text="تقدير الأرباح والخسائر الشهرية:", font=("Tajawal", 12, "bold"), bg="#ffffff").pack(anchor="w", padx=32, pady=(10,2))
        self.monthly_tree = ttk.Treeview(self, columns=("المنتج/الخدمة", "وحدات البيع", "تكلفة الوحدة", "إجمالي التكلفة", "سعر البيع", "إجمالي الإيراد", "إجمالي الربح"), show="headings", height=5)
        for col in self.monthly_tree["columns"]:
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=110)
        self.monthly_tree.pack(padx=28, pady=7)

        # تقدير الإيرادات السنوية
        tk.Label(self, text="تقدير الإيرادات السنوية:", font=("Tajawal", 12, "bold"), bg="#ffffff").pack(anchor="w", padx=32, pady=(10,2))
        self.yearly_tree = ttk.Treeview(self, columns=("الشهر", "منتج 1", "منتج 2", "منتج 3", "منتج 4", "الإجمالي"), show="headings", height=5)
        for col in self.yearly_tree["columns"]:
            self.yearly_tree.heading(col, text=col)
            self.yearly_tree.column(col, width=85)
        self.yearly_tree.pack(padx=28, pady=7)