import tkinter as tk
from tkinter import ttk

class ProductionFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        tk.Label(self, text="مستلزمات الإنتاج", font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="e", pady=16, padx=24)

        # جدول المعدات/الآلات/الأثاث
        tk.Label(self, text="المعدات/الآلات/الأثاث:", font=("Tajawal", 13), bg="#ffffff", fg="#173360").pack(anchor="w", padx=32)
        self.equip_tree = ttk.Treeview(self, columns=("البند", "العدد", "سعر الوحدة", "الإجمالي"), show="headings", height=5)
        for col in self.equip_tree["columns"]:
            self.equip_tree.heading(col, text=col)
            self.equip_tree.column(col, width=140)
        self.equip_tree.pack(padx=28, pady=7)

        # جدول المواد الخام
        tk.Label(self, text="المواد الخام (شهرياً):", font=("Tajawal", 13), bg="#ffffff", fg="#173360").pack(anchor="w", padx=32, pady=(18,0))
        self.raw_tree = ttk.Treeview(self, columns=("المادة", "العدد", "سعر الوحدة", "الإجمالي"), show="headings", height=5)
        for col in self.raw_tree["columns"]:
            self.raw_tree.heading(col, text=col)
            self.raw_tree.column(col, width=140)
        self.raw_tree.pack(padx=28, pady=7)