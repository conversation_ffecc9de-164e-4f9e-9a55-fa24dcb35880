import tkinter as tk

class SummaryFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master, bg="#ffffff")
        tk.Label(self, text="ملخص الربح السنوي", font=("<PERSON>jawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(anchor="e", pady=18, padx=24)
        fields = [
            "إجمالي الإيرادات",
            "إجمالي التكاليف السنوية",
            "قيمة إهلاك الأصول",
            "صافي الأرباح"
        ]
        form = tk.Frame(self, bg="#ffffff")
        form.pack(anchor="e", padx=32)
        self.entries = {}
        for i, label in enumerate(fields):
            tk.Label(form, text=label, font=("Tajawal", 12), bg="#ffffff", width=18, anchor="e").grid(row=i, column=0, sticky="e", pady=8, padx=8)
            ent = tk.Entry(form, font=("Tajawal", 12), width=32, justify="right")
            ent.grid(row=i, column=1, pady=8, padx=8)
            self.entries[label] = ent